using Android.Content;
using SQLite;
using System;
using System.Linq;

namespace nz.co.haroco.reminderplus
{
    public class ReminderItem
    {
        [PrimaryKey, AutoIncrement]
        public int ID { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public AlarmPriority Priority { get; set; }
        public int TimesIgnored { get; set; }
        public DateTime CreatedDateTimeUTC { get; set; }
        public DateTime ReminderDateTimeUTC { get; set; }
        public DateTime DismissedDateTimeUTC { get; set; }
        public bool IsDismissed { get; set; }
        public string Log => $"[#{ID} '{Title.Substring(0, Title.Length > 25 ? 25 : Title.Length)}']";

        public ReminderItem()
        {
            Priority = AlarmPriority.High;
            CreatedDateTimeUTC = DateTime.Now.ToUniversalTime();
            IsDismissed = false;
        }

        public override string ToString()
        {
            return Title + "\nReminder - " + ReminderDateTimeUTC.ToLocalTime().ToStringNZ();
        }

        public void ResetTimesIgnored()
        {
            TimesIgnored = -1;
        }
    }
}