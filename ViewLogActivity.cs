using Android.App;
using Android.Content;
using Android.OS;
using Android.Runtime;
using Android.Views;
using Android.Widget;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using Environment = System.Environment;

namespace nz.co.haroco.reminderplus
{
    [Activity(Label = "View Log")]
    public class ViewLogActivity : Activity
    {
        private TextView textView;

        protected override void OnCreate(Bundle savedInstanceState)
        {
            base.OnCreate(savedInstanceState);

            // Create your application here

            SetContentView(Resource.Layout.ViewLog);
            textView = FindViewById<TextView>(Resource.Id.textViewLog);

            string fileName = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Personal), "CalendarEventAlerts.txt");
            if (File.Exists(fileName))
            {
                string text = File.ReadAllText(fileName, Encoding.UTF8);

                textView.Text = text;
            }
        }
    }
}