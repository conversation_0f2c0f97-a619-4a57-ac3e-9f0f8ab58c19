using Android.App;
using Android.Content;
using Android.OS;
using System;

namespace nz.co.haroco.reminderplus
{
    [Activity(Label = "ReminderPlus")]
    public class PopupActivity : Activity
    {
        protected override void OnCreate(Bundle savedInstanceState)
        {
            base.OnCreate(savedInstanceState);

            Intent intent = this.Intent;
            int id = intent.GetIntExtra("id", -1);
            string title = intent.GetStringExtra("title");
            string description = intent.GetStringExtra("description");

            //Will use an AlertDialog
            //https://tsicilian.wordpress.com/2012/03/29/android-sms-popup-part-three-the-activity/

            var builder = new AlertDialog.Builder(this);
            builder.SetMessage(title);
            builder.SetCancelable(false);
            builder.SetPositiveButton("OK", HandlePositiveButtonClick);

            var alert = builder.Create();
            
            alert.Show();  
        }

        internal void HandlePositiveButtonClick(object sender, EventArgs e)
        {
            this.Finish();
        }
    }
}