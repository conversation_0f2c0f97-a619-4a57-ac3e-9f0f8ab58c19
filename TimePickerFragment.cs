using Android.App;
using Android.OS;
using Android.Widget;
using System;

namespace nz.co.haroco.reminderplus
{
    internal class TimePickerFragment : DialogFragment, TimePickerDialog.IOnTimeSetListener
    {
        // TAG can be any string of your choice.
        public static readonly string TAG = "X:" + typeof(DatePickerFragment).Name.ToUpper();

        // Initialize this value to prevent NullReferenceExceptions.
        private Action<DateTime> _timeSelectedHandler = delegate { };

        public static TimePickerFragment NewInstance(Action<DateTime> onTimeSelected)
        {
            return new TimePickerFragment() { _timeSelectedHandler = onTimeSelected };
        }

        public override Dialog OnCreateDialog(Bundle savedInstanceState)
        {
            DateTime currently = DateTime.Now;
            TimePickerDialog dialog = new TimePickerDialog(Activity,
                                                           this,
                                                           currently.Hour,
                                                           currently.Minute,
                                                           false);
            return dialog;
        }

        public void OnTimeSet(TimePicker view, int hour, int minute)
        {
            DateTime selectedTime = new DateTime(1, 1, 1, hour, minute, 0);
            _timeSelectedHandler(selectedTime);
        }
    }
}