using Android.Content;
using Android.Util;
using System;
using System.Runtime.CompilerServices;

namespace nz.co.haroco.reminderplus
{
    [BroadcastReceiver]
    public class AlarmReceiver : BroadcastReceiver
    {
        public override void OnReceive(Context context, Intent intent)
        {
            LogJ.Info($"Alarm broadcast received");
            // This receives alarms that our app has created, either:
            // (a) a genuine reminder (which we should pop a notification for), OR
            // (b) an 'inaction' alarm (where we remind the user that there is a notification they should attend to)

            // Note that a calendar event that our app doesn't know about will 'arrive' via CalendarReminderReceiver
            // and will therefore never trigger option (a) above, but it could result in (b) if the user ignores it

            // Get data from intent (which we would have inserted when creating the alarm originally)
            var id = intent.GetIntExtra("id", -1);
            var alarmTimeTicks = intent.GetLongExtra("alarmtimeticks", -1);
            var expectedAlarmTime = new DateTime(alarmTimeTicks);

            // Find the reminder associated with this alarm
            var reminder = ReminderPlusCore.GetReminder(context, id);

            if (reminder != null)
            {
                // Increment the reminder's TimesIgnored value and save it to the database
                reminder.TimesIgnored++;
                LogJ.Info($"<-- Alarm broadcast identified, timesIgnored: {reminder.TimesIgnored}", reminder);
                ReminderPlusCore.SaveReminder(context, reminder);

                // Notify the user (some flavour of notification dependent on whether this is case (a) or (b) above)
                LogJ.Info("Requesting NotifyUser", reminder);
                ReminderPlusCore.NotifyUser(context, reminder, expectedAlarmTime);
            }
            else
                LogJ.Error("ERROR: No matching event found for alarm!", reminder);
        }
    }

    public enum AlarmPriority
    {
        Low,
        Medium,
        High
    }
}