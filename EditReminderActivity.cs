using Android.App;
using Android.Content;
using Android.Graphics;
using Android.OS;
using Android.Text;
using Android.Text.Style;
using Android.Widget;
using System;

namespace nz.co.haroco.reminderplus
{
    [Activity(Label = "Edit Reminder")]
    public class EditReminderActivity : Activity
    {
        public ReminderItem reminder;

        protected override void OnCreate(Bundle savedInstanceState)
        {
            base.OnCreate(savedInstanceState);

            SetContentView(Resource.Layout.EditItem);

            RadioButton radioPriorityLow = FindViewById<RadioButton>(Resource.Id.radioPriorityLow);
            RadioButton radioPriorityMedium = FindViewById<RadioButton>(Resource.Id.radioPriorityMedium);
            RadioButton radioPriorityHigh = FindViewById<RadioButton>(Resource.Id.radioPriorityHigh);

            Button button10seconds = FindViewById<Button>(Resource.Id.button10seconds);
            Button button5minutes = FindViewById<Button>(Resource.Id.button5mins);
            Button button15minutes = FindViewById<Button>(Resource.Id.button15minutes);
            Button button30minutes = FindViewById<Button>(Resource.Id.button30minutes);
            Button button1hour = FindViewById<Button>(Resource.Id.button1hour);
            Button button1day = FindViewById<Button>(Resource.Id.button1day);
            Button buttonDateTime = FindViewById<Button>(Resource.Id.buttonDateTime);
            Button buttonDismiss = FindViewById<Button>(Resource.Id.buttonDismiss);

            button10seconds.Click += ButtonDelaySeconds_Click;
            button5minutes.Click += ButtonDelaySeconds_Click;
            button15minutes.Click += ButtonDelaySeconds_Click;
            button30minutes.Click += ButtonDelaySeconds_Click;
            button1hour.Click += ButtonDelaySeconds_Click;
            button1day.Click += ButtonDelaySeconds_Click;
            buttonDateTime.Click += ButtonDateTime_Click;
            buttonDismiss.Click += ButtonDismiss_Click;

            //TODO: Can this activity only be called via the one intent?
            //TODO: ie where we *know* the id will be provided?

            Intent intent = this.Intent;

            int id = intent.GetIntExtra("id", -1);

            reminder = ReminderPlusCore.GetReminder(this, id);

            if (reminder != null)
            {
                TextView tvDescription = FindViewById<TextView>(Resource.Id.textViewDescription);

                switch (reminder.Priority)
                {
                    case AlarmPriority.Low:
                        radioPriorityLow.Checked = true;
                        break;

                    case AlarmPriority.Medium:
                        radioPriorityMedium.Checked = true;
                        break;

                    case AlarmPriority.High:
                        radioPriorityHigh.Checked = true;
                        break;
                }

                var desc = new SpannableStringBuilder();

                desc.Append(reminder.ID + " - ");
                int titleStart = desc.Length();

                desc.Append(reminder.Title);
                int titleEnd = desc.Length();

                desc.Append($"\n{reminder.Description}");
                desc.Append($"\n{reminder.ReminderDateTimeUTC.ToLocalTime().ToStringNZ()}");

                // Make the title larger and bold
                desc.SetSpan(new StyleSpan(TypefaceStyle.Bold), titleStart, titleEnd, SpanTypes.ExclusiveExclusive);
                desc.SetSpan(new RelativeSizeSpan(1.5f), 0, titleEnd, SpanTypes.ExclusiveExclusive);

                tvDescription.TextFormatted = desc;

                tvDescription.MovementMethod = new Android.Text.Method.ScrollingMovementMethod();
            }
        }

        private void ButtonDelaySeconds_Click(object sender, EventArgs e)
        {
            Button button = (Button)sender;
            int seconds = Convert.ToInt32(button.Tag.ToString());
            reminder.ResetTimesIgnored();
            reminder.IsDismissed = false;
            reminder.ReminderDateTimeUTC = DateTime.UtcNow + new TimeSpan(0, 0, seconds);
            reminder.Priority = GetPriority();
            ReminderPlusCore.SaveReminder(this, reminder);
            ReminderPlusCore.CreateReminderAlarm(this, reminder);
            this.Finish();
        }

        private void ButtonDateTime_Click(object sender, EventArgs e)
        {
            reminder.Priority = GetPriority();
            DatePickerFragment frag = DatePickerFragment.NewInstance(dt =>
            {
                TimePickerFragment frag = TimePickerFragment.NewInstance(dtTime =>
                {
                    // Increment the chosen date by the time just selected
                    dt = dt.AddHours(dtTime.Hour);
                    dt = dt.AddMinutes(dtTime.Minute);

                    reminder.ResetTimesIgnored();
                    reminder.IsDismissed = false;
                    reminder.ReminderDateTimeUTC = dt.ToUniversalTime();

                    ReminderPlusCore.SaveReminder(this, reminder);
                    ReminderPlusCore.CreateReminderAlarm(this, reminder);

                    Finish();
                });

                frag.Show(FragmentManager, TimePickerFragment.TAG);
            });

            frag.Show(FragmentManager, DatePickerFragment.TAG);
        }

        private void ButtonDismiss_Click(object sender, EventArgs e)
        {
            ReminderPlusCore.DismissReminder(this, reminder);
            this.Finish();
        }

        private AlarmPriority GetPriority()
        {
            RadioButton radioPriorityLow = FindViewById<RadioButton>(Resource.Id.radioPriorityLow);
            RadioButton radioPriorityMedium = FindViewById<RadioButton>(Resource.Id.radioPriorityMedium);

            if (radioPriorityLow.Checked)
                return AlarmPriority.Low;
            else if (radioPriorityMedium.Checked)
                return AlarmPriority.Medium;
            return AlarmPriority.High;
        }
    }
}