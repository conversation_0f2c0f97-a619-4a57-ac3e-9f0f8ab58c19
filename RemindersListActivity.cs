using Android.App;
using Android.Content;
using Android.OS;
using Android.Runtime;
using Android.Util;
using Android.Widget;
using System.Collections.Generic;

namespace nz.co.haroco.reminderplus
{
    [Activity(Label = "RemindersListActivity")]
    public class RemindersListActivity : Activity
    {
        private List<ReminderItem> reminders;
        private ListView listView;
        private string dataType = "";

        protected override void OnCreate(Bundle savedInstanceState)
        {
            base.OnCreate(savedInstanceState);

            // Create your application here
            SetContentView(Resource.Layout.ActiveReminders);

            dataType = Intent.GetStringExtra("data");

            this.Title = dataType;

            listView = FindViewById<ListView>(Resource.Id.listViewReminders);
            listView.Clickable = true;
            listView.ItemClick += ListView_ItemClick;

            RefreshList();
        }

        private void RefreshList()
        {
            if (dataType == "Active Reminders")
                reminders = ReminderPlusCore.GetAllUndismissedReminders(this);
            else if (dataType == "Recently Dismissed Reminders")
                reminders = ReminderPlusCore.GetRecentlyDismissedReminders(this);
            else if (dataType == "Recent Reminders")
                reminders = ReminderPlusCore.GetUndismissedAndPoppedReminders(this);
            else
                LogJ.Error("Invalid data passed in intent");

            var listAdapter = new ArrayAdapter<ReminderItem>(this, Android.Resource.Layout.SimpleListItem1, reminders);

            listView.Adapter = listAdapter;
        }

        private void ListView_ItemClick(object sender, AdapterView.ItemClickEventArgs e)
        {
            ReminderItem reminder = reminders[e.Position];

            Intent intent = new Intent(this, typeof(EditReminderActivity));
            intent.PutExtra("id", reminder.ID);

            StartActivityForResult(intent, 1);
        }

        protected override void OnActivityResult(int requestCode, [GeneratedEnum] Result resultCode, Intent data)
        {
            base.OnActivityResult(requestCode, resultCode, data);

            RefreshList();
        }
    }
}