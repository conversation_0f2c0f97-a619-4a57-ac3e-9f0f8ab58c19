using Java.Lang.Reflect;
using System.Runtime.CompilerServices;
using System.Text.RegularExpressions;

namespace nz.co.haroco.reminderplus
{
    public static class LogJ
    {
        public static void Info(string message, ReminderItem reminder = null, [CallerFilePath] string filePath = null, [CallerMemberName] string memberName = null, [CallerLineNumber] int lineNumber = 0)
        {
            Android.Util.Log.Info(GetPrefix(reminder, filePath, memberName, lineNumber), message);
        }

        public static void Warn(string message, ReminderItem reminder = null, [CallerFilePath] string filePath = null, [CallerMemberName] string memberName = null, [CallerLineNumber] int lineNumber = 0)
        {
            Android.Util.Log.Warn(GetPrefix(reminder, filePath, memberName, lineNumber), message);
        }

        public static void Error(string message, ReminderItem reminder = null, [CallerFilePath] string filePath = null, [CallerMemberName] string memberName = null, [CallerLineNumber] int lineNumber = 0)
        {
            Android.Util.Log.Error(GetPrefix(reminder, filePath, memberName, lineNumber), message);
        }

        private static string GetPrefix(ReminderItem reminder, string filePath, string memberName, int lineNumber)
        {
            return $"RPlus::{GetFileName(filePath)}.{memberName}:{lineNumber} {(reminder == null ? "" : $"{reminder.Log}")}"; 
        }

        private static string GetFileName(string filePath)
        {
            Match match = Regex.Match(filePath, @"\\(?<filename>[^\\]*)\.cs$");

            if (match.Success)
                return match.Groups["filename"].Value;

            return "<Unknown>";
        }
    }
}