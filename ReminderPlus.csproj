<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFramework>net9.0-android</TargetFramework>
        <SupportedOSPlatformVersion>21</SupportedOSPlatformVersion>
        <OutputType>Exe</OutputType>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <ApplicationId>nz.co.haroco.reminderplus</ApplicationId>
        <ApplicationVersion>1</ApplicationVersion>
        <ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
        <!--
          Enables trim analyzers and full trimming during Release mode.
          To learn more, see: https://learn.microsoft.com/dotnet/core/deploying/trimming/trimming-options#trimming-granularity
        -->
        <TrimMode>full</TrimMode>
        <AndroidGenerateResourceDesigner>true</AndroidGenerateResourceDesigner>
        <AndroidUseAapt2>true</AndroidUseAapt2>
    </PropertyGroup>
    <ItemGroup>
      <PackageReference Include="sqlite-net-pcl" Version="1.9.172" />
      <PackageReference Include="Xamarin.AndroidX.AppCompat" Version="1.6.1.5" />
    </ItemGroup>
</Project>