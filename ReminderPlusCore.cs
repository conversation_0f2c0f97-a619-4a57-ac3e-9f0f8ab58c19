using Android.App;
using Android.Content;
using Android.OS;
using Android.Runtime;
using Android.Text.Format;
using Android.Util;
using Android.Widget;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;

namespace nz.co.haroco.reminderplus
{
    internal class ReminderPlusCore
    {
        private static ReminderApplication GetApp(Context context)
        {
            return (ReminderApplication)context.ApplicationContext;
        }

        /// <summary>
        /// Adds or updates a reminder to the database
        /// </summary>
        internal static void SaveReminder(Context context, ReminderItem item)
        {
            GetApp(context).DataManager.SaveReminder(item);
            LogJ.Info("Saving Reminder", item);
        }

        /// <summary>
        /// Finds the reminder matching the specified ID in the database
        /// </summary>
        internal static ReminderItem GetReminder(Context context, int id)
        {
            return GetApp(context).DataManager.GetReminder(id);
        }

        /// <summary>
        /// Gets all 'active' reminders ie those that are currently undismissed, ordered by reminder date
        /// </summary>
        internal static List<ReminderItem> GetAllUndismissedReminders(Context context)
        {
            return GetApp(context).DataManager.GetReminders()
                .Where(x => x.IsDismissed == false)
                .OrderBy(x => x.ReminderDateTimeUTC)
                .ToList();
        }

        /// <summary>
        /// Gets all reminders which are currently undismissed and past their reminder date ie recently popped, ordered by reminder date
        /// </summary>
        internal static List<ReminderItem> GetUndismissedAndPoppedReminders(Context context)
        {
            return GetApp(context).DataManager.GetReminders()
                .Where(x => x.IsDismissed == false && x.ReminderDateTimeUTC < DateTime.UtcNow)
                .OrderByDescending(x => x.ReminderDateTimeUTC)
                .Take(50)
                .ToList();
        }

        /// <summary>
        /// Get recently dismissed reminders
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        internal static List<ReminderItem> GetRecentlyDismissedReminders(Context context)
        {
            return GetApp(context).DataManager.GetReminders()
                .Where(x => x.IsDismissed == true)
                .OrderByDescending(x => x.DismissedDateTimeUTC)
                .Take(50)
                .ToList();
        }

        /// <summary>
        /// Mark the specified reminder as dismissed (also clears the notification)
        /// </summary>
        internal static void DismissReminder(Context context, ReminderItem reminder)
        {
            LogJ.Info("User has dismissed this reminder", reminder);
            reminder.DismissedDateTimeUTC = DateTime.Now.ToUniversalTime();
            reminder.IsDismissed = true;
            GetApp(context).DataManager.SaveReminder(reminder);

            ClearNotification(context, reminder);
            ClearAlarm(context, reminder);
        }

        /// <summary>
        /// Clears the specified notification from the notification dropdown
        /// </summary>
        private static void ClearNotification(Context context, ReminderItem reminder)
        {
            //Clear notification from screen
            NotificationManager notificationManager = (NotificationManager)context.GetSystemService(Context.NotificationService);
            notificationManager.Cancel(reminder.ID);

            LogJ.Info("Notification dismissed (if any)", reminder);
        }

        /// <summary>
        /// Notifies the user (via a notification or vibration)
        /// </summary>
        internal static void NotifyUser(Context context, ReminderItem reminder, DateTime? intendedAlarmTime = null)
        {
            // Determine which notification channel to use
            string channelID = "nz.co.haroco.reminderplus.ALARM_DEFAULT";

            if (reminder.TimesIgnored > 0)
            {
                channelID = reminder.Priority switch
                {
                    AlarmPriority.Low => "nz.co.haroco.reminderplus.ALARM_LOW",
                    AlarmPriority.Medium => "nz.co.haroco.reminderplus.ALARM_MEDIUM",
                    AlarmPriority.High => "nz.co.haroco.reminderplus.ALARM_HIGH",
                    _ => "nz.co.haroco.reminderplus.ALARM_HIGH"
                };
            }

            LogJ.Info($"Notification will use channelID: {channelID}", reminder);

            // Clear any existing alarms for this reminder
            ClearAlarm(context, reminder);

            // Create a new intent to allow the user to edit this item and attach it to a notification
            var resultIntent = new Intent(context, typeof(EditReminderActivity));
            resultIntent.SetFlags(ActivityFlags.NewTask | ActivityFlags.ClearTask);
            resultIntent.PutExtra("id", reminder.ID);
            var pending = PendingIntent.GetActivity(context, reminder.ID, resultIntent, PendingIntentFlags.CancelCurrent | PendingIntentFlags.Immutable);

            // Create another intent to handle the situation where the user swipes/dismisses the notification
            var dismissedIntent = new Intent(context, typeof(NotificationDismissedReceiver));
            dismissedIntent.PutExtra("id", reminder.ID);
            var pendingDismissed = PendingIntent.GetBroadcast(context.ApplicationContext, reminder.ID, dismissedIntent, PendingIntentFlags.Immutable);

            int delayMinutes = 0;
            
            if (intendedAlarmTime.HasValue)
                delayMinutes = (DateTime.Now - intendedAlarmTime.Value.ToLocalTime()).Minutes;

            var builder =
                new Notification.Builder(context, channelID)
                    .SetContentTitle(reminder.Title)
                    .SetContentText($"Reminded: {reminder.ReminderDateTimeUTC.ToLocalTime().ToStringNZ()}{(delayMinutes > 0 ? " +" + delayMinutes + " mins" : "")}")
                    .SetSmallIcon(Resource.Drawable.rp_notification)
                    .SetContentIntent(pending) // The 'main' intent, when the user clicks on a reminder notification to edit it
                    .SetDeleteIntent(pendingDismissed); // The intent when the notification is dismissed/swiped

            var notification = builder.Build();

            LogJ.Info($"Pushing notification to user", reminder);
            NotificationManager.FromContext(context).Notify(reminder.ID, notification);

            Toast toast = Toast.MakeText(Application.Context, $"{reminder.Log}", ToastLength.Long);
            toast.Show();

            // Set an alarm so that we can remind the user if they don't respond to the notification after certain amount of time
            // If they action (delay or dismiss) the reminder then the alarm will be cleared
            CreateInactionAlarm(context, reminder);
        }

        /// <summary>
        /// Creates an inaction alarm for the specified reminder
        /// </summary>
        private static void CreateInactionAlarm(Context context, ReminderItem reminder)
        {
            LogJ.Info("Inaction Alarm requested", reminder);
            // The delay (in mins) for each successive notification ignore
            List<int> delaysLow = new List<int>() { 5 };
            List<int> delaysMed = new List<int>() { 5, 30, 60 };
            List<int> delaysHigh = new List<int>() { 1, 5, 10, 15, 30, 45, 60 };

            TimeSpan? delay = reminder.Priority switch
            {
                AlarmPriority.Low => GetDelay(delaysLow, reminder.TimesIgnored),
                AlarmPriority.Medium => GetDelay(delaysMed, reminder.TimesIgnored),
                AlarmPriority.High => GetDelay(delaysHigh, reminder.TimesIgnored),
                _ => GetDelay(delaysLow, reminder.TimesIgnored)
            };

            // Local function to calculate the delay needed
            static TimeSpan? GetDelay(List<int> delays, int timesIgnored)
            {
                if (delays.Count >= timesIgnored + 1)
                {
                    // The delay minutes is the value in the delay list, less the value before it in the list
                    // ie the third delay of 5, 30, 60 would be 60 - 30 = 30 mins
                    int targetTime = delays[timesIgnored];
                    int mostRecentAlarm = timesIgnored == 0 ? 0 : delays.Skip(timesIgnored - 1).Take(1).First();

                    return new TimeSpan(0, targetTime - mostRecentAlarm, 0);
                }

                // No further alarms are required
                return null;
            }

            if (delay.HasValue)
            {
                LogJ.Info($"Inaction alarm will have a delay of {delay.Value} mins", reminder);
                CreateAlarm(context, reminder, DateTime.UtcNow.Add(delay.Value));
            }
            else
                LogJ.Info("No further inaction alarms required", reminder);
        }

        /// <summary>
        /// Creates a reminder alarm for the specified reminder using its currently configured reminder time
        /// </summary>
        internal static void CreateReminderAlarm(Context context, ReminderItem reminder)
        {
            LogJ.Info($"Reminder Alarm requested: {reminder.ReminderDateTimeUTC.ToStringNZWithSeconds()} | Local: {reminder.ReminderDateTimeUTC.ToLocalTime().ToStringNZWithSeconds()} | UTC: {reminder.ReminderDateTimeUTC.ToUniversalTime()}", reminder);
            ClearNotification(context, reminder);
            CreateAlarm(context, reminder, reminder.ReminderDateTimeUTC);
        }

        /// <summary>
        /// Registers an alarm with the Android AlarmManager service
        /// </summary>
        private static void CreateAlarm(Context context, ReminderItem reminder, DateTime dateTimeOfAlarmUTC)
        {
            // Clear any pending alarms for this reminder
            // This is needed if the user edits a notification for a later time. In that case we
            // would already have created an inaction alarm that is no longer needed/valid
            ClearAlarm(context, reminder);

            // Register an alarm
            var alarmIntent = new Intent(context, typeof(AlarmReceiver));
            alarmIntent.PutExtra("id", reminder.ID);

            // Find the amount of time (in ms) since Jan 1st 1970 (POSIX time, or Unix epoch time)
            long timeInMillis = (long)dateTimeOfAlarmUTC.Subtract(new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc)).TotalMilliseconds;

            alarmIntent.PutExtra("alarmtimeticks", dateTimeOfAlarmUTC.Ticks);

            var pending = PendingIntent.GetBroadcast(context, reminder.ID, alarmIntent, PendingIntentFlags.UpdateCurrent | PendingIntentFlags.Immutable);

            var alarmManager = context.GetSystemService(Context.AlarmService).JavaCast<AlarmManager>();
            alarmManager.SetExactAndAllowWhileIdle(AlarmType.RtcWakeup, timeInMillis, pending);

            LogJ.Info($"Alarm set for {dateTimeOfAlarmUTC.ToLocalTime().ToStringNZWithSeconds()}", reminder);

            Toast toast = Toast.MakeText(Application.Context, $"{reminder.Log} Alarm set for " + dateTimeOfAlarmUTC.ToLocalTime().ToStringNZWithSeconds(), ToastLength.Short);
            toast.Show();
        }

        private static void ClearAlarm(Context context, ReminderItem reminder)
        {
            LogJ.Info("Clearing any pending alarms", reminder);
            var alarmManager = context.GetSystemService(Context.AlarmService).JavaCast<AlarmManager>();
            var alarmIntent = new Intent(context, typeof(AlarmReceiver));
            var pending = PendingIntent.GetBroadcast(context, reminder.ID, alarmIntent, PendingIntentFlags.UpdateCurrent | PendingIntentFlags.Immutable);

            alarmManager.Cancel(pending);
        }

        internal static void RestoreReminders(Context context)
        {
            LogJ.Info("Restoring reminders");

            //Get a list of undismissed reminders
            List<ReminderItem> reminders = ReminderPlusCore.GetAllUndismissedReminders(context);

            foreach (ReminderItem reminder in reminders)
            {
                //Each of these needs to have an alarm created.
                //In theory, ones that 'fired' while the device was off will pop due to the reminder date/time being in the past
                reminder.ResetTimesIgnored();
                CreateReminderAlarm(context, reminder);
            }
        }
    }
}