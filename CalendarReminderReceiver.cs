using Android.App;
using Android.Content;
using Android.Database;
using Android.Provider;
using Android.Util;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace nz.co.haroco.reminderplus
{
    [BroadcastReceiver]
    class CalendarReminderReceiver : BroadcastReceiver
    {
        public override void OnReceive(Context context, Intent intent)
        {
            LogJ.Info("Calendar Event alert received");

            string fileName = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Personal), "CalendarEventAlerts.txt");
            string existingText;
            if (File.Exists(fileName))
                existingText = File.ReadAllText(fileName, Encoding.UTF8);
            else
                existingText = "";

            StringBuilder sb = new StringBuilder(System.DateTime.Now + "-CalAlert-");

            // This handles alarms/alerts created in the calendar (not by this app)
            // Therefore we need to pull information from it to determine which calendar event it corresponds to.

            // Get the calendar alerts from the calendar content provider.
            // The LastPathSegment (effectively the alarm time) can be used to find an alert with a matching alarm time.

            // NOTE: if the calendar event is created shortly before it's due to 'fire' (ie within 1-2 mins) we have 
            // observed the LastPathSegment (ie the alarm time) not actually matching the expected alarm time.
            // eg set event for 3:00pm (current time 2:59pm) and the alert may fire at 2:59:40 pm and thus lastpathsegment
            // will have a time that doesn't match any events in the calendar!
            List<CalendarAlertData> alerts = GetEventIDs(context, intent.Data.LastPathSegment);

            // We may have more than one matching alert eg if two events were scheduled for the same time
            // This case should be able to be handled by processing one of the alerts (arbitrarily), and then
            // picking up the other one when the receiver is called again (should get two calls in quick succession).
            if (alerts.Count > 0)
            {
                CalendarAlertData alertData = null;

                // Get first result that has the correct state
                foreach (CalendarAlertData a in alerts)
                {
                    sb.Append(a.State + "-");
                    if (a.State != CalendarAlertsState.Dismissed)
                    {
                        alertData = a;
                    }
                }

                if (alertData != null)
                {
                    sb.Append("AD-");

                    // We have an alert with a matching alarm time and it is not already dismissed.
                    // Get the calendar event associated with this alert.
                    CalendarEventData eventData = GetEvent(context, alertData.EventID);

                    if (eventData != null)
                    {
                        sb.Append(eventData.Title);

                        // We found the corresponding event so should be able to handle this event/reminder from here on out.
                        // Mark the alert as dismissed
                        MarkAlertAsDismissed(alertData.ID);

                        // Create a new ReminderItem so that our app can track it going forward.
                        ReminderItem reminder = new ReminderItem()
                        {
                            Title = eventData.Title,
                            Description = eventData.Description,
                            TimesIgnored = 0
                        };

                        reminder.ReminderDateTimeUTC = DateTime.UtcNow;

                        // Add the reminder to the database
                        ReminderPlusCore.SaveReminder(context, reminder);

                        // Create a notification to display to the user. They can then click to edit the reminder, or swipe to dismiss it
                        // This will also create an 'inaction' alarm (if they fail to respond to the notification)
                        ReminderPlusCore.NotifyUser(context, reminder);

                        // Force the user to the Recently Popped Reminders screen
                        var newIntent = new Intent(context, typeof(EditReminderActivity));
                        newIntent.PutExtra("id", reminder.ID);
                        newIntent.AddFlags(ActivityFlags.NewTask);
                        context.StartActivity(newIntent);
                    }
                    else
                        LogJ.Error("ERROR: No matching event found for calendar alert!");
                }
                else
                    LogJ.Warn("WARNING: No alerts in a non-dismissed state.");
            }
            else
                LogJ.Error("ERROR: No matching alert found for calendar alert!");

            File.WriteAllText(fileName, sb.ToString() + System.Environment.NewLine + existingText, Encoding.UTF8);
        }

        private List<CalendarAlertData> GetEventIDs(Context context, string alarmTime)
        {
            // A lot of this is covered here - https://developer.android.com/guide/topics/providers/content-provider-basics.html
            // This is the URI used for calendar alerts (defined in Android). 
            // We'll use this to talk to the correct ContentProvider
            var uri = CalendarContract.CalendarAlerts.ContentUri;

            // The projection determines which data fields we wish to return in the query
            string[] projection =
            {
                "_id",
                CalendarContract.CalendarAlertsColumns.EventId,
                CalendarContract.CalendarAlertsColumns.State,
                CalendarContract.CalendarAlertsColumns.AlarmTime
            };

            // The selection is used to filter the results, essentially a 'where' clause.
            // In this case we're looking for an alert with an alarm time matching what came through in the broadcast
            string selection = CalendarContract.CalendarAlertsColumns.AlarmTime + "=" + alarmTime;

            // Create a cursor using the above information to get back a set of results
            var loader = new CursorLoader(context, uri, projection, selection, null, null);
            var cursor = (ICursor)loader.LoadInBackground();

            List<CalendarAlertData> results = new List<CalendarAlertData>();

            StringBuilder sb = new StringBuilder();

            // Grab all the matching results
            if (cursor.MoveToFirst())
            {
                do
                {
                    var x = new CalendarAlertData() { ID = cursor.GetInt(0), EventID = cursor.GetInt(1), State = (CalendarAlertsState)cursor.GetInt(2), AlarmTime = cursor.GetLong(3) };
                    results.Add(x);
                    sb.AppendLine(x.ToString());
                }
                while (cursor.MoveToNext());
            }
            return results;
        }

        private CalendarEventData GetEvent(Context context, int eventID)
        {
            var uri = CalendarContract.Events.ContentUri;
            string[] projection =
            {
                CalendarContract.EventsColumns.Title,
                CalendarContract.EventsColumns.Description
            };

            string selection = "_id=" + eventID;

            var loader = new CursorLoader(context, uri, projection, selection, null, null);
            var cursor = (ICursor)loader.LoadInBackground();

            // We'll just return the first result as we shouldn't get more than one match!
            if (cursor.MoveToFirst())
                return new CalendarEventData() { Title = cursor.GetString(0), Description = cursor.GetString(1) };
            else
                return null;
        }

        private void MarkAlertAsDismissed(int alert_id)
        {
            // Create a URI which points to the exact alert record we wish to deal with
            var uri = Android.Net.Uri.WithAppendedPath(CalendarContract.CalendarAlerts.ContentUri, alert_id.ToString());

            ContentValues valuesToModify = new ContentValues();
            valuesToModify.Put(CalendarContract.CalendarAlertsColumns.State, (int)CalendarAlertsState.Dismissed);

            ContentResolver cr = Application.Context.ContentResolver;
            cr.Update(uri, valuesToModify, null, null);
        }
    }

    class CalendarAlertData
    {
        public int ID { get; set; }
        public int EventID { get; set; }
        public CalendarAlertsState State { get; set; }
        public long AlarmTime { get; set; }

        public override string ToString()
        {
            return ID + ", " + EventID + ", " + AlarmTime;
        }
    }

    class CalendarEventData
    {
        public string Title { get; set; }
        public string Description { get; set; }
    }
}