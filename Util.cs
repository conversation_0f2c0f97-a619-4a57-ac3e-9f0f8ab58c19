using System;
using Android.Util;

namespace nz.co.haroco.reminderplus
{
    public static class Util
    {
        public static DateTime GetLocalTimeFromEpoch(long milliseconds)
        {
            var epoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            return epoch.AddMilliseconds(milliseconds).ToLocalTime();
        }

        public static long GetEpochMillisecondsNow()
        {
            DateTime now = DateTime.Now.ToUniversalTime();
            return (long)now.Subtract(new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc)).Subtract(new TimeSpan(1,0,0,0)).TotalMilliseconds;
        }

        public static string ToStringNZ(this DateTime dt)
        {
            return dt.ToString("ddd MMM d yyyy, H:mm");
        }

        public static string ToStringNZWithSeconds(this DateTime dt)
        {
            return dt.ToString("ddd MMM d yyyy, H:mm:ss");
        }
    }
}