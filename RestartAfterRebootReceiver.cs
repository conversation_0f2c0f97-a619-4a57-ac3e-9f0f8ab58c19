using Android.Content;
using Android.Util;
using Android.Widget;
using System.Collections.Generic;

namespace nz.co.haroco.reminderplus
{
    [BroadcastReceiver]
    public class RestartAfterRebootReceiver : BroadcastReceiver
    {
        public override void OnReceive(Context context, Intent intent)
        {
            //Device has rebooted so we'll need to reschedule the outstanding alarms
            Toast.MakeText(context, "ReminderPlus - device has rebooted!", ToastLength.Long).Show();

            ReminderPlusCore.RestoreReminders(context);           
        }
    }
}