using Android.App;
using Android.OS;
using Android.Widget;
using System;

namespace nz.co.haroco.reminderplus
{
    [Activity(Label = "Create Reminder")]
    public class CreateReminderActivity : Activity
    {
        private ReminderItem reminder = null;
        private EditText title;
        private RadioButton radioPriorityLow;
        private RadioButton radioPriorityMedium;
        private RadioButton radioPriorityHigh;

        protected override void OnCreate(Bundle savedInstanceState)
        {
            base.OnCreate(savedInstanceState);

            // Set our view from the "main" layout resource
            SetContentView(Resource.Layout.CreateItem);

            Button button = FindViewById<Button>(Resource.Id.buttonAddItem);
            title = FindViewById<EditText>(Resource.Id.editTextTitle);
            radioPriorityLow = FindViewById<RadioButton>(Resource.Id.radioPriorityLow);
            radioPriorityMedium = FindViewById<RadioButton>(Resource.Id.radioPriorityMedium);
            radioPriorityHigh = FindViewById<RadioButton>(Resource.Id.radioPriorityHigh);

            button.Click += OnAddItemButtonClick;
            title.EditorAction += Title_EditorAction; // When the enter key is pressed
            title.InputType = Android.Text.InputTypes.TextFlagCapWords;
            title.RequestFocus();
        }

        private void Title_EditorAction(object sender, TextView.EditorActionEventArgs e)
        {
            // User has pressed enter in the text field
            OnAddItemButtonClick(sender, e);
        }

        private void OnAddItemButtonClick(object o, EventArgs e)
        {
            // User has clicked the button (or pressed the enter key while editing)
            reminder = new ReminderItem()
            {
                Title = title.Text,
                TimesIgnored = -1,
                Priority = radioPriorityLow.Checked ? AlarmPriority.Low : (radioPriorityMedium.Checked ? AlarmPriority.Medium : AlarmPriority.High)
            };

            // Allow the user to select the date and time for the reminder
            DatePickerFragment frag = DatePickerFragment.NewInstance(dt =>
            {
                TimePickerFragment frag = TimePickerFragment.NewInstance(dtTime =>
                {
                    // Increment the chosen date by the time just selected
                    dt = dt.AddHours(dtTime.Hour);
                    dt = dt.AddMinutes(dtTime.Minute);

                    reminder.ReminderDateTimeUTC = dt.ToUniversalTime();

                    ReminderPlusCore.SaveReminder(this, reminder);
                    ReminderPlusCore.CreateReminderAlarm(this, reminder);

                    Finish();
                });

                frag.Show(FragmentManager, TimePickerFragment.TAG);
            });
            frag.Show(FragmentManager, DatePickerFragment.TAG);
        }
    }
}