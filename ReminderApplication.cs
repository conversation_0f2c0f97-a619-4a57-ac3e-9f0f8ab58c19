using Android.App;
using Android.Runtime;
using System;

namespace nz.co.haroco.reminderplus
{
    [Application]
    class ReminderApplication : Application
    {
        private DataManager dMgr;

        public DataManager DataManager
        {
            get
            {
                if (dMgr == null)
                    dMgr = new DataManager();

                return dMgr;
            }
        }

        public ReminderApplication(IntPtr handle, JniHandleOwnership transfer)
            : base(handle, transfer)
        {
            // this is optional, just a suggestion
            dMgr = new DataManager();
        }

        public override void OnCreate()
        {
            base.OnCreate();

            // Create the notification channels used by our app
            var manager = NotificationManager.FromContext(this);

            // Default = initial notifications ie standard sound and vibration
            var notificationChannelDefault = new NotificationChannel($"nz.co.haroco.reminderplus.ALARM_DEFAULT", "Default", NotificationImportance.High);
            notificationChannelDefault.EnableVibration(true);
            notificationChannelDefault.LockscreenVisibility = NotificationVisibility.Public;
            manager.CreateNotificationChannel(notificationChannelDefault);

            // Low = no sound, but additional vibration (inaction alarms only)
            var notificationChannelLow = new NotificationChannel($"nz.co.haroco.reminderplus.ALARM_LOW", "Low", NotificationImportance.High);
            notificationChannelLow.LockscreenVisibility = NotificationVisibility.Public;
            //notificationChannelLow.SetSound(null, null);
            notificationChannelLow.EnableVibration(true);
            notificationChannelLow.SetVibrationPattern(new long[] { 0, 500, 1000, 500 });
            manager.CreateNotificationChannel(notificationChannelLow);

            // Medium = no sound, but additional vibration (inaction alarms only)
            var notificationChannelMedium = new NotificationChannel($"nz.co.haroco.reminderplus.ALARM_MEDIUM", "Medium", NotificationImportance.High);
            notificationChannelMedium.LockscreenVisibility = NotificationVisibility.Public;
            //notificationChannelMedium.SetSound(null, null);
            notificationChannelMedium.EnableVibration(true);
            notificationChannelMedium.SetVibrationPattern(new long[] { 0, 800, 1000, 800 });
            manager.CreateNotificationChannel(notificationChannelMedium);

            // High = no sound, but additional vibration (inaction alarms only)
            var notificationChannelHigh = new NotificationChannel($"nz.co.haroco.reminderplus.ALARM_HIGH", "High", NotificationImportance.High);
            notificationChannelHigh.LockscreenVisibility = NotificationVisibility.Public;
            //notificationChannelHigh.SetSound(null, null);
            notificationChannelHigh.EnableVibration(true);
            notificationChannelHigh.SetVibrationPattern(new long[] { 0, 1500, 1000, 1000, 1000, 1500 });
            manager.CreateNotificationChannel(notificationChannelHigh);
        }
    }
}