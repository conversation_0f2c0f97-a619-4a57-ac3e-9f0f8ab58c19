using SQLite;
using System.Collections.Generic;
using System.IO;

namespace nz.co.haroco.reminderplus
{
    class DataManager
    {
        private readonly SQLiteConnection database;

        private readonly string libraryPath;
        private readonly string masterConnectionString;
        private readonly string backupConnectionString;
        private const string masterDB = "master.db";
        private const string backupDB = "backup.db";

        public DataManager()
        {
            libraryPath = System.Environment.GetFolderPath(System.Environment.SpecialFolder.Personal);
            masterConnectionString = Path.Combine(libraryPath, masterDB);
            backupConnectionString = Path.Combine(libraryPath, backupDB);

            database = new SQLiteConnection(masterConnectionString);

            // This will create the table if it doesn't already exist
            database.CreateTable<ReminderItem>();
        }

        internal List<ReminderItem> GetReminders()
        {
            return database.Table<ReminderItem>().ToList();
        }

        internal ReminderItem GetReminder(int id)
        {
            return database.Table<ReminderItem>().Where(x => x.ID == id).FirstOrDefault();
        }

        internal int SaveReminder(ReminderItem reminder)
        {
            if (reminder.ID != 0)
                return database.Update(reminder);
            else
                return database.Insert(reminder);
        }

        internal void BackupDatabase()
        {
            if (File.Exists(masterConnectionString))
            {
                LogJ.Info("Backing up database");
                File.Copy(masterConnectionString, backupConnectionString, true);
            }
        }

        internal void RestoreDatabase()
        {
            if (File.Exists(backupConnectionString))
            {
                LogJ.Info("Restoring database");
                File.Copy(backupConnectionString, masterConnectionString, true);
            }
        }

        internal void DeleteDatabase()
        {
            if (File.Exists(masterConnectionString))
            {
                LogJ.Info("Deleting database");

                database.DropTable<ReminderItem>();
                database.CreateTable<ReminderItem>();
            }
        }
    }
}