
using Android.App;
using Android.Content;
using Android.Widget;

namespace nz.co.haroco.reminderplus
{
    [BroadcastReceiver]
    public class NotificationDismissedReceiver : BroadcastReceiver
    {
        public override void OnReceive(Context context, Intent intent)
        {
            //Handle notification being dismissed eg swiped away

            //Find reminder linked to this notification
            int id = intent.GetIntExtra("id", -1);

            if (id == -1)
            {
                Toast toast = Toast.MakeText(Application.Context, "ERROR! ID not found in notification.", ToastLength.Short);
                toast.Show();
            }
            else
            {
                var reminder = ReminderPlusCore.GetReminder(context, id);

                if (reminder != null)
                {
                    ReminderPlusCore.DismissReminder(context, reminder);
                }
            }
        }
    }
}