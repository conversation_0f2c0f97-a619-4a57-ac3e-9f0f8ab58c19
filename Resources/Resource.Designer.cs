#pragma warning disable 1591
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

[assembly: global::Android.Runtime.ResourceDesignerAttribute("nz.co.haroco.reminderplus.Resource", IsApplication=true)]

namespace nz.co.haroco.reminderplus
{
	
	
	[global::System.CodeDom.Compiler.GeneratedCodeAttribute("Xamarin.Android.Build.Tasks", "13.2.2.120")]
	public partial class Resource
	{
		
		static Resource()
		{
			global::Android.Runtime.ResourceIdManager.UpdateIdValues();
		}
		
		public static void UpdateIdValues()
		{
		}
		
		public partial class Attribute
		{
			
			// aapt resource value: 0x7F010000
			public const int font = **********;
			
			// aapt resource value: 0x7F010001
			public const int fontProviderAuthority = **********;
			
			// aapt resource value: 0x7F010002
			public const int fontProviderCerts = **********;
			
			// aapt resource value: 0x7F010003
			public const int fontProviderFetchStrategy = **********;
			
			// aapt resource value: 0x7F010004
			public const int fontProviderFetchTimeout = **********;
			
			// aapt resource value: 0x7F010005
			public const int fontProviderPackage = **********;
			
			// aapt resource value: 0x7F010006
			public const int fontProviderQuery = **********;
			
			// aapt resource value: 0x7F010007
			public const int fontStyle = **********;
			
			// aapt resource value: 0x7F010008
			public const int fontWeight = **********;
			
			static Attribute()
			{
				global::Android.Runtime.ResourceIdManager.UpdateIdValues();
			}
			
			private Attribute()
			{
			}
		}
		
		public partial class Color
		{
			
			// aapt resource value: 0x7F020000
			public const int notification_action_color_filter = **********;
			
			// aapt resource value: 0x7F020001
			public const int notification_icon_bg_color = **********;
			
			// aapt resource value: 0x7F020002
			public const int ripple_material_light = **********;
			
			// aapt resource value: 0x7F020003
			public const int secondary_text_default_material_light = **********;
			
			static Color()
			{
				global::Android.Runtime.ResourceIdManager.UpdateIdValues();
			}
			
			private Color()
			{
			}
		}
		
		public partial class Dimension
		{
			
			// aapt resource value: 0x7F030000
			public const int compat_button_inset_horizontal_material = 2130903040;
			
			// aapt resource value: 0x7F030001
			public const int compat_button_inset_vertical_material = 2130903041;
			
			// aapt resource value: 0x7F030002
			public const int compat_button_padding_horizontal_material = 2130903042;
			
			// aapt resource value: 0x7F030003
			public const int compat_button_padding_vertical_material = 2130903043;
			
			// aapt resource value: 0x7F030004
			public const int compat_control_corner_material = 2130903044;
			
			// aapt resource value: 0x7F030005
			public const int compat_notification_large_icon_max_height = 2130903045;
			
			// aapt resource value: 0x7F030006
			public const int compat_notification_large_icon_max_width = 2130903046;
			
			// aapt resource value: 0x7F030007
			public const int notification_action_icon_size = 2130903047;
			
			// aapt resource value: 0x7F030008
			public const int notification_action_text_size = 2130903048;
			
			// aapt resource value: 0x7F030009
			public const int notification_big_circle_margin = 2130903049;
			
			// aapt resource value: 0x7F03000A
			public const int notification_content_margin_start = 2130903050;
			
			// aapt resource value: 0x7F03000B
			public const int notification_large_icon_height = 2130903051;
			
			// aapt resource value: 0x7F03000C
			public const int notification_large_icon_width = 2130903052;
			
			// aapt resource value: 0x7F03000D
			public const int notification_main_column_padding_top = 2130903053;
			
			// aapt resource value: 0x7F03000E
			public const int notification_media_narrow_margin = 2130903054;
			
			// aapt resource value: 0x7F03000F
			public const int notification_right_icon_size = 2130903055;
			
			// aapt resource value: 0x7F030010
			public const int notification_right_side_padding_top = 2130903056;
			
			// aapt resource value: 0x7F030011
			public const int notification_small_icon_background_padding = 2130903057;
			
			// aapt resource value: 0x7F030012
			public const int notification_small_icon_size_as_large = 2130903058;
			
			// aapt resource value: 0x7F030013
			public const int notification_subtext_size = 2130903059;
			
			// aapt resource value: 0x7F030014
			public const int notification_top_pad = 2130903060;
			
			// aapt resource value: 0x7F030015
			public const int notification_top_pad_large_text = 2130903061;
			
			static Dimension()
			{
				global::Android.Runtime.ResourceIdManager.UpdateIdValues();
			}
			
			private Dimension()
			{
			}
		}
		
		public partial class Drawable
		{
			
			// aapt resource value: 0x7F050000
			public const int notification_action_background = 2131034112;
			
			// aapt resource value: 0x7F050001
			public const int notification_bg = 2131034113;
			
			// aapt resource value: 0x7F050002
			public const int notification_bg_low = 2131034114;
			
			// aapt resource value: 0x7F050003
			public const int notification_bg_low_normal = 2131034115;
			
			// aapt resource value: 0x7F050004
			public const int notification_bg_low_pressed = 2131034116;
			
			// aapt resource value: 0x7F050005
			public const int notification_bg_normal = 2131034117;
			
			// aapt resource value: 0x7F050006
			public const int notification_bg_normal_pressed = 2131034118;
			
			// aapt resource value: 0x7F050007
			public const int notification_icon_background = 2131034119;
			
			// aapt resource value: 0x7F050008
			public const int notification_template_icon_bg = 2131034120;
			
			// aapt resource value: 0x7F050009
			public const int notification_template_icon_low_bg = 2131034121;
			
			// aapt resource value: 0x7F05000A
			public const int notification_tile_bg = 2131034122;
			
			// aapt resource value: 0x7F05000B
			public const int notify_panel_notification_icon_bg = 2131034123;
			
			// aapt resource value: 0x7F05000C
			public const int rp_notification = 2131034124;
			
			static Drawable()
			{
				global::Android.Runtime.ResourceIdManager.UpdateIdValues();
			}
			
			private Drawable()
			{
			}
		}
		
		public partial class Id
		{
			
			// aapt resource value: 0x7F060000
			public const int action0 = **********;
			
			// aapt resource value: 0x7F060005
			public const int actions = 2131099653;
			
			// aapt resource value: 0x7F060001
			public const int action_container = **********;
			
			// aapt resource value: 0x7F060002
			public const int action_divider = **********;
			
			// aapt resource value: 0x7F060003
			public const int action_image = **********;
			
			// aapt resource value: 0x7F060004
			public const int action_text = 2131099652;
			
			// aapt resource value: 0x7F060006
			public const int async = 2131099654;
			
			// aapt resource value: 0x7F060007
			public const int blocking = 2131099655;
			
			// aapt resource value: 0x7F060008
			public const int button10seconds = 2131099656;
			
			// aapt resource value: 0x7F060009
			public const int button1hour = 2131099657;
			
			// aapt resource value: 0x7F06000A
			public const int button1min = 2131099658;
			
			// aapt resource value: 0x7F06000B
			public const int button30mins = 2131099659;
			
			// aapt resource value: 0x7F06000C
			public const int button30seconds = 2131099660;
			
			// aapt resource value: 0x7F06000D
			public const int button5mins = 2131099661;
			
			// aapt resource value: 0x7F06000E
			public const int buttonActiveReminders = 2131099662;
			
			// aapt resource value: 0x7F06000F
			public const int buttonAddItem = 2131099663;
			
			// aapt resource value: 0x7F060010
			public const int buttonBackupDB = 2131099664;
			
			// aapt resource value: 0x7F060011
			public const int buttonCreateItem = 2131099665;
			
			// aapt resource value: 0x7F060012
			public const int buttonDateTime = 2131099666;
			
			// aapt resource value: 0x7F060013
			public const int buttonDeleteDB = 2131099667;
			
			// aapt resource value: 0x7F060014
			public const int buttonDismiss = 2131099668;
			
			// aapt resource value: 0x7F060015
			public const int buttonRecentReminders = 2131099669;
			
			// aapt resource value: 0x7F060016
			public const int buttonRecentlyDismissed = 2131099670;
			
			// aapt resource value: 0x7F060017
			public const int buttonRegister = 2131099671;
			
			// aapt resource value: 0x7F060018
			public const int buttonRestoreDB = 2131099672;
			
			// aapt resource value: 0x7F060019
			public const int buttonViewLog = 2131099673;
			
			// aapt resource value: 0x7F06001A
			public const int cancel_action = 2131099674;
			
			// aapt resource value: 0x7F06001B
			public const int chronometer = 2131099675;
			
			// aapt resource value: 0x7F06001C
			public const int end_padder = 2131099676;
			
			// aapt resource value: 0x7F06001D
			public const int forever = 2131099677;
			
			// aapt resource value: 0x7F06001E
			public const int icon = 2131099678;
			
			// aapt resource value: 0x7F06001F
			public const int icon_group = 2131099679;
			
			// aapt resource value: 0x7F060020
			public const int info = 2131099680;
			
			// aapt resource value: 0x7F060021
			public const int italic = 2131099681;
			
			// aapt resource value: 0x7F060022
			public const int line1 = 2131099682;
			
			// aapt resource value: 0x7F060023
			public const int line3 = 2131099683;
			
			// aapt resource value: 0x7F060024
			public const int listViewReminders = 2131099684;
			
			// aapt resource value: 0x7F060025
			public const int media_actions = 2131099685;
			
			// aapt resource value: 0x7F060026
			public const int normal = 2131099686;
			
			// aapt resource value: 0x7F060027
			public const int notification_background = 2131099687;
			
			// aapt resource value: 0x7F060028
			public const int notification_main_column = 2131099688;
			
			// aapt resource value: 0x7F060029
			public const int notification_main_column_container = 2131099689;
			
			// aapt resource value: 0x7F06002A
			public const int radioPriorityHigh = 2131099690;
			
			// aapt resource value: 0x7F06002B
			public const int radioPriorityLow = 2131099691;
			
			// aapt resource value: 0x7F06002C
			public const int radioPriorityMedium = 2131099692;
			
			// aapt resource value: 0x7F06002D
			public const int right_icon = 2131099693;
			
			// aapt resource value: 0x7F06002E
			public const int right_side = 2131099694;
			
			// aapt resource value: 0x7F06002F
			public const int status_bar_latest_event_content = 2131099695;
			
			// aapt resource value: 0x7F060030
			public const int tag_accessibility_actions = 2131099696;
			
			// aapt resource value: 0x7F060031
			public const int tag_accessibility_clickable_spans = 2131099697;
			
			// aapt resource value: 0x7F060032
			public const int tag_accessibility_heading = 2131099698;
			
			// aapt resource value: 0x7F060033
			public const int tag_accessibility_pane_title = 2131099699;
			
			// aapt resource value: 0x7F060034
			public const int tag_on_apply_window_listener = 2131099700;
			
			// aapt resource value: 0x7F060035
			public const int tag_on_receive_content_listener = 2131099701;
			
			// aapt resource value: 0x7F060036
			public const int tag_on_receive_content_mime_types = 2131099702;
			
			// aapt resource value: 0x7F060037
			public const int textViewLog = 2131099703;
			
			// aapt resource value: 0x7F060038
			public const int textViewOutput = 2131099704;
			
			// aapt resource value: 0x7F060039
			public const int time = 2131099705;
			
			// aapt resource value: 0x7F06003A
			public const int title = 2131099706;
			
			// aapt resource value: 0x7F06003B
			public const int titleEditText = 2131099707;
			
			static Id()
			{
				global::Android.Runtime.ResourceIdManager.UpdateIdValues();
			}
			
			private Id()
			{
			}
		}
		
		public partial class Integer
		{
			
			// aapt resource value: 0x7F070000
			public const int status_bar_notification_info_maxnum = 2131165184;
			
			static Integer()
			{
				global::Android.Runtime.ResourceIdManager.UpdateIdValues();
			}
			
			private Integer()
			{
			}
		}
		
		public partial class Layout
		{
			
			// aapt resource value: 0x7F080000
			public const int ActiveReminders = 2131230720;
			
			// aapt resource value: 0x7F080001
			public const int CreateItem = 2131230721;
			
			// aapt resource value: 0x7F080002
			public const int EditItem = 2131230722;
			
			// aapt resource value: 0x7F080003
			public const int Main = 2131230723;
			
			// aapt resource value: 0x7F080004
			public const int notification_action = 2131230724;
			
			// aapt resource value: 0x7F080005
			public const int notification_action_tombstone = 2131230725;
			
			// aapt resource value: 0x7F080006
			public const int notification_media_action = 2131230726;
			
			// aapt resource value: 0x7F080007
			public const int notification_media_cancel_action = 2131230727;
			
			// aapt resource value: 0x7F080008
			public const int notification_template_big_media = 2131230728;
			
			// aapt resource value: 0x7F080009
			public const int notification_template_big_media_custom = 2131230729;
			
			// aapt resource value: 0x7F08000A
			public const int notification_template_big_media_narrow = 2131230730;
			
			// aapt resource value: 0x7F08000B
			public const int notification_template_big_media_narrow_custom = 2131230731;
			
			// aapt resource value: 0x7F08000C
			public const int notification_template_custom_big = 2131230732;
			
			// aapt resource value: 0x7F08000D
			public const int notification_template_icon_group = 2131230733;
			
			// aapt resource value: 0x7F08000E
			public const int notification_template_lines_media = 2131230734;
			
			// aapt resource value: 0x7F08000F
			public const int notification_template_media = 2131230735;
			
			// aapt resource value: 0x7F080010
			public const int notification_template_media_custom = 2131230736;
			
			// aapt resource value: 0x7F080011
			public const int notification_template_part_chronometer = 2131230737;
			
			// aapt resource value: 0x7F080012
			public const int notification_template_part_time = 2131230738;
			
			// aapt resource value: 0x7F080013
			public const int ViewLog = 2131230739;
			
			static Layout()
			{
				global::Android.Runtime.ResourceIdManager.UpdateIdValues();
			}
			
			private Layout()
			{
			}
		}
		
		public partial class Mipmap
		{
			
			// aapt resource value: 0x7F090000
			public const int rp_launcher = 2131296256;
			
			static Mipmap()
			{
				global::Android.Runtime.ResourceIdManager.UpdateIdValues();
			}
			
			private Mipmap()
			{
			}
		}
		
		public partial class String
		{

			// aapt resource value: 0x7F0A0000
			public const int ApplicationName = 2131361792;

			// aapt resource value: 0x7F0A0001
			public const int status_bar_notification_info_overflow = 2131361793;

			static String()
			{
				global::Android.Runtime.ResourceIdManager.UpdateIdValues();
			}

			private String()
			{
			}
		}

		public partial class Style
		{

			// aapt resource value: 0x7F0B0000
			public const int TextAppearance_Compat_Notification = 2131427328;

			// aapt resource value: 0x7F0B0001
			public const int TextAppearance_Compat_Notification_Info = 2131427329;

			// aapt resource value: 0x7F0B0002
			public const int TextAppearance_Compat_Notification_Line2 = 2131427330;

			// aapt resource value: 0x7F0B0003
			public const int TextAppearance_Compat_Notification_Time = 2131427331;

			// aapt resource value: 0x7F0B0004
			public const int TextAppearance_Compat_Notification_Title = 2131427332;

			// aapt resource value: 0x7F0B0005
			public const int Widget_Compat_NotificationActionContainer = 2131427333;

			// aapt resource value: 0x7F0B0006
			public const int Widget_Compat_NotificationActionText = 2131427334;

			static Style()
			{
				global::Android.Runtime.ResourceIdManager.UpdateIdValues();
			}

			private Style()
			{
			}
		}

		public partial class Styleable
		{

			// aapt resource value: { 0x7F010001,0x7F010002,0x7F010003,0x7F010004,0x7F010005,0x7F010006 }
			public static int[] FontFamily = new int[] {
					**********,
					**********,
					**********,
					**********,
					**********,
					**********};

			// aapt resource value: { 0x1010532,0x1010533,0x101053F,0x7F010000,0x7F010007,0x7F010008 }
			public static int[] FontFamilyFont = new int[] {
					16844082,
					16844083,
					16844095,
					**********,
					**********,
					**********};

			// aapt resource value: 1
			public const int FontFamily_fontProviderAuthority = 1;

			// aapt resource value: 2
			public const int FontFamily_fontProviderCerts = 2;

			// aapt resource value: 3
			public const int FontFamily_fontProviderFetchStrategy = 3;

			// aapt resource value: 4
			public const int FontFamily_fontProviderFetchTimeout = 4;

			// aapt resource value: 5
			public const int FontFamily_fontProviderPackage = 5;

			// aapt resource value: 0
			public const int FontFamily_fontProviderQuery = 0;

			// aapt resource value: 0
			public const int FontFamilyFont_android_font = 0;

			// aapt resource value: 2
			public const int FontFamilyFont_android_fontStyle = 2;

			// aapt resource value: 1
			public const int FontFamilyFont_android_fontVariationSettings = 1;

			// aapt resource value: 3
			public const int FontFamilyFont_android_fontWeight = 3;

			// aapt resource value: 4
			public const int FontFamilyFont_android_ttcIndex = 4;

			// aapt resource value: 5
			public const int FontFamilyFont_font = 5;

			// aapt resource value: 6
			public const int FontFamilyFont_fontStyle = 6;

			// aapt resource value: 7
			public const int FontFamilyFont_fontVariationSettings = 7;

			// aapt resource value: 8
			public const int FontFamilyFont_fontWeight = 8;

			// aapt resource value: 9
			public const int FontFamilyFont_ttcIndex = 9;

			static Styleable()
			{
				global::Android.Runtime.ResourceIdManager.UpdateIdValues();
			}

			private Styleable()
			{
			}
		}
	}
}
